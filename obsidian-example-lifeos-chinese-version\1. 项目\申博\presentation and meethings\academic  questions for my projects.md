---
tags:
  - phd-meethings
  - 项目/申博
cssclasses:
  - column-page
---

2024-08-13 12:14

信息来源 1  ：[x.com](https://x.com/leviyuen/status/1824611632716320799)

信息来源 2：

信息来源 3：
信息来源 4：


# 1 费曼学习法+英语
<content>最近悟出来一个既能快速学习知识，又能提高英语水平的学习方法，即费曼学习+英文解释+刻意练习

1.将你要学习的PDF上传到GPT或者claude
2.告诉AI你用费曼学习+英文解释的方法帮助你理解这篇材料。
3.向AI提问，让他用英文出问题考你。
4.关键：自己用英文默念回答一遍，如果回答不上来，再去学习相关概念，直到用英文能把概念解释得很清楚（如果不行采用 IELTS口语2+1 方法）。

费曼学习的核心就是：当你能给人解释清楚一个概念时，才算完全理解。而你能用英文解释清楚时，代表你能用母语人的思维方式去解释一个复杂概念，这个过程中你需要对你的英语表达方式进行整合，并且用口语输出，这个过程很慢，但对你英语有巨大提升。

5.让AI纠正你的问题。这是《刻意练习》的核心概念：通过正确答案的反馈，有目的的纠正自己的理解，语法，表达，词汇，从而让你大脑走出舒适区。建立新的心理表征。

这个过程很痛苦，但是同时学习知识和英文效率很高的方式。</content>
![image.png|600x600](https://notion-lgd.oss-cn-beijing.aliyuncs.com/20241123114028.png)

### 1.1.1 具体实施步骤
- PDF材料上传至AI平台
- 设定学习方法
- AI提供英文问题
- 英文口语练习
- AI反馈和纠正






# 2 vasp 赝势问题




# 3 ReaxFF 

## 3.1 势函数原理

A potential function in physics and materials science describes how particles or atoms in a system interact with each other. It's essentially a mathematical formula that calculates the energy of the system based on the positions of the particles.

The goal of a potential function is to predict the forces between atoms and how they will move or arrange themselves. For example:

- **If atoms are too close together**, the potential function shows a high energy, which means they repel each other.
- **If atoms are at the right distance**, the energy is low, indicating a stable arrangement.
- **If atoms are too far apart**, the energy increases again, representing weak attraction or no interaction.

In simple terms, a potential function helps us simulate and understand how atoms "behave" in a material, enabling predictions of properties like structure, stability, and reactions.


==3. how you investigate the H 2 O adsorption and dissociation on Zr surface？==
**Water Molecule Adsorption:** Studied the adsorption of four water molecules and found that adsorption at the top position was most stable.

**Water Dissociation Process:** Water at the top position serves as the initial state, while HCP-H and fcc-OH serve as the final state. During the dissociation process calculation, it was found that TOP-H 2 O first migrates to the neighboring BRI position before dissociation, followed by the breaking of the H-O bond.

**Interstitial Site Calculations:** The insertion energy was calculated using the formula: E_in = E_total - E_surface - E_adsorbate. Possible diffusion processes were discussed:

- For H: The lowest diffusion barrier is from HCP to T 1
- For O: The diffusion path is FCC-HCP-T 1

==4. what you investigate  Surface substitution==
Surface substitution: Study the adsorption energy of H and O at different distances from substitutional elements.

==5. what information you get from ELF and Charge Analysis？==

**Charge Analysis Findings:**
We studied the influence of alloying elements on the charge distribution in the Zr matrix through charge density isosurfaces and electron localization function analysis. The results showed that Sn and Cr significantly altered the charge distribution, while Nb and Fe had relatively minor effects.

**ELF Analysis Results:**
The ELF (Electron Localization Function) analysis investigated the chemical bonding characteristics between oxygen atoms and their surrounding zirconium atoms and alloying elements. It was found that O-Zr and O-Nb bonds tend to be covalent in nature, while O bonds with Fe, Cr, and Sn tend to be ionic.


==6. how you get the diffusion equation==
This equation has already been derived by others. We are considering the case with alloying elements, and we only considered different occupancy situations to perform a new derivation



# 7 machine learning for interstiti atoms
==1. Why study the stability of interstitial atoms: ==
Interstitial atoms can affect the strength and plasticity of metals and enhance radiation resistance. For example, in refractory metals (such as V, Mo, W), the stability of oxygen interstitial atoms has a significant impact on the high-temperature structural stability of materials.

==2. what Principles of Confident Learning:==
Confident learning works by estimating the joint distribution between true labels and noisy labels, identifying and filtering potential erroneous samples, then retraining the model with cleaned data to improve model performance when label noise exists.

==3. what is Hume's Law==
- Atomic sizes should be similar, with radius difference not exceeding 15%
- Crystal structures should be identical or similar
- Electronegativity should be close (small electronegativity difference)
- Valence states should be the same, or the solvent metal can dissolve solute metals with higher valence states (ionization energy difference)
==4. Weighting Method:==
If the property at position i is a, N is the coordination number of interstitial atoms, b is the solid angle from the Voronoi polyhedron. The weighted feature is sum^N b*a (The sum from 1 to N of b multiplied by a)

==5. Model Evaluation Methods:==
- True Positive (TP): Number of samples correctly predicted as positive class
- True Negative (TN): Number of samples correctly predicted as negative class
- False Positive (FP): Number of negative samples incorrectly predicted as positive (false positives)
- False Negative (FN): Number of positive samples incorrectly predicted as negative (false negatives)

NPV (Negative Predictive Value) = TN / (TN+FN)

- Represents the proportion of actual negative samples among predicted negative samples
- High NPV means the model is more reliable in identifying negative samples

FOR (False Omission Rate) = FN / (TN+FN)

- Represents the proportion of actual positives incorrectly predicted as negative
- Low FOR means the model misses fewer true positive samples

==6. SHAP Principles==
It assigns a contribution value to each feature, showing how much impact each feature has on the final prediction. Specifically, SHAP calculates the individual effect of each feature and combines them to see how they influence model predictions under different conditions. This helps us understand which features positively or negatively influence results and their specific contribution magnitude.

==7. Feature Impacts:==
AW represents polyhedral angle - high AW values lead to unstable interstitials while low values increase stability. d-X-host has a nonlinear relationship with stability, with optimal stability in the best distance range. Low d-X-sub values stabilize interstitials while high values reduce stability.

==8. Machine Learning Algorithm Selection:==
Various models were chosen to enable comparison and selection of the most suitable model for specific tasks, and to demonstrate that SISSO-obtained descriptors can be applied across multiple machine learning models.


==9. **Model Generalization Assessment:** ==
Used new interstitial structures that weren't used in training.
==10. Validation set labeling: 
manually labeled data identified as incorrectly labeled using the previous process.==

==10. **Descriptor Search Method:** ==
Discussed different complexities - fcomplexity in SISSO represents feature complexity, defined as the number of operators in the feature. Selected two-dimensional descriptors that performed best on training set for training.


==11. **RSH, RSI, ENH Impacts and Significance:**==

- RSH represents radius difference between host and substitutional atoms - larger differences cause more severe lattice distortion and increase formation energy
- RSI represents radius difference between interstitial and substitutional elements - large differences reduce formation energy
- ENH is electronegativity difference - larger differences form stronger chemical bonds and reduce formation energy

# 8 Corrosion mechanism
## 8.1 Corrosion mechanism of Zr alloys


The **corrosion mechanism of Zirconium (Zr)** is primarily related to its oxidation reaction in high-temperature water environments, especially in severe conditions such as nuclear reactors. The corrosion behavior of zirconium and its alloys is influenced by multiple factors, including temperature, oxide layer formation, presence of oxygen and hydrogen, water chemistry, and alloy microstructure.

### 8.1.1 **Zr Corrosion Process**
1. **Oxidation Reaction**: Zirconium undergoes oxidation in water or steam environments, producing zirconium dioxide (ZrO₂) and hydrogen. The basic reaction equation is:
   $$
   Zr + 2H_2O \rightarrow ZrO_2 + 2H_2
   $$
   During this process, a protective oxide film (ZrO₂) forms on the zirconium surface, which can prevent further oxidation and corrosion[1][2].

16. **Oxide Film Formation and Degradation**: The initially formed oxide film is relatively dense and can effectively prevent oxygen diffusion inward, thus slowing down corrosion. However, as time progresses, the oxide film thickness increases, and cracks or pores may develop, leading to decreased protection. Particularly in nuclear reactors, the oxide film may fail due to factors such as irradiation and temperature fluctuations, accelerating corrosion[3][4].

17. **Hydrogen Absorption and Embrittlement**: During the corrosion process, part of the generated hydrogen escapes, but some hydrogen atoms may diffuse into the zirconium matrix. When the hydrogen content exceeds its solubility limit, it precipitates as hydrides (such as ZrH₂). These hydrides are brittle and lead to decreased mechanical properties, increasing the risk of cracking[1][5].

18. **Types of Corrosion**:
   - **Uniform Corrosion**: This is the most common form of corrosion, typically occurring in nuclear reactors. The corrosion rate gradually decreases over time, but may accelerate under certain conditions, such as high-temperature and high-pressure water environments[5].
   - **Nodular Corrosion**: Local areas may experience accelerated corrosion due to differences in microstructure or water chemistry[1][6].
   - **Shadow Corrosion**: This form is similar to uniform corrosion but is characterized by more severe corrosion in areas that contact other materials[6].

### 8.1.2 **Influencing Factors**
- **Temperature and Pressure**: Corrosion rates of zirconium alloys significantly increase in high-temperature and high-pressure environments. Particularly in nuclear reactors, high-temperature water or steam accelerates oxide film degradation[2][4].
- **Water Chemistry**: Dissolved oxygen, hydrogen, and other impurities (such as boron, lithium) in the coolant affect the corrosion behavior of zirconium alloys. For example, in environments containing dissolved hydrogen, zirconium alloys are more susceptible to hydrogen absorption, leading to hydrogen embrittlement[1][5].
- **Alloy Composition and Microstructure**: Different types of zirconium alloys (such as Zircaloy-2, Zircaloy-4) have varying corrosion resistance due to their microstructure and alloying elements (such as tin, niobium). For example, niobium-containing alloys typically have better hydrogen absorption resistance[5][6].

In conclusion, the corrosion resistance of zirconium and its alloys in high-temperature water environments primarily depends on the formation of a dense oxide film on their surface. However, during long-term use, this protective film may fail due to various factors, leading to accelerated corrosion and hydrogen embrittlement. Understanding these mechanisms is crucial for extending material lifetime and improving nuclear fuel efficiency.

Citations:
[1] https://www.nrc.gov/docs/ML1525/ML15253A227.pdf
[2] https://www.totalmateria.com/en-us/articles/corrosion-of-zirconium-alloys-1/
[3] https://ora.ox.ac.uk/objects/uuid:264c8c64-1a59-437a-8334-2bf129481b42/files/sgf06g3729
[4] https://www.tandfonline.com/doi/full/10.1080/00223131.2022.2127954
[5] https://www.nuce.psu.edu/Motta/Publications/124_Motta_ARoMR_2015.pdf
[6] https://www.antinternational.com/docs/samples/FM/02/First_chapter_-__ZIRAT12_STR__Corrosion_Mechanisms.pdf
[7] https://www.mtialbany.com/metals/zirconium/

## 8.2 how use DFT and other methods to design novel Zr alloys
Here's the English translation in Markdown format:

### 8.2.1 Zr Oxide Film Cracking
Using DFT to simulate the interaction between Zr alloy surfaces with oxygen and water molecules can provide a deeper understanding of oxide film formation processes and their stability. DFT can provide information about atomic arrangements, electronic structures, and energy states within the oxide film, helping to predict oxide film behavior under different environmental conditions.

### 8.2.2 Zr Hydrogen Absorption and Hydrogen Embrittlement Research
DFT can be used to study hydrogen adsorption characteristics on zirconium alloy surfaces and evaluate the effects of different alloy compositions on hydrogen absorption. By calculating hydrogen adsorption energy on different crystal faces (such as Zr (0001)), it's possible to identify which alloying elements can effectively inhibit hydrogen adsorption, thereby reducing hydrogen embrittlement phenomena.

### 8.2.3 Zr Design Machine Learning Methods
- **Data-driven Materials Design**: Machine learning algorithms can extract features from large amounts of experimental data to build predictive models. For example, by analyzing relationships between different zirconium alloy compositions, microstructures, and corrosion behaviors, machine learning can help identify optimal alloy formulations.
- **Text Data Analysis**: Combined with natural language processing technology, information about zirconium alloy corrosion behavior can be extracted from literature and transformed into data usable for deep learning models. This approach can improve model accuracy in predicting corrosion potential and help discover new corrosion-resistant alloy combinations.

# 9 the Corrosion mechanism of Mg alloys
Magnesium (Mg) alloys are known for their lightweight and desirable mechanical properties, making them useful in various industries such as aerospace, automotive, and biomedical applications. However, their major drawback is their susceptibility to corrosion, especially in aqueous environments. The corrosion mechanism of Mg alloys is primarily electrochemical and involves several key processes:

### 9.1.1 **1. Electrochemical Corrosion Reactions**
The corrosion of Mg alloys in aqueous environments typically involves anodic dissolution of magnesium and cathodic hydrogen evolution:

- **Anodic reaction**: 
  $$
  \text{Mg} \rightarrow \text{Mg}^{2+} + 2e^-
  $$
  Magnesium atoms lose electrons and dissolve as $$Mg ^{2+}$$ ions.

- **Cathodic reaction**: 
  $$
  2H_2O + 2e^- \rightarrow H_2\uparrow + 2OH^-
  $$
  or
  $$
  2H^+ + 2e^- \rightarrow H_2\uparrow
  $$
  In this reaction, water or hydrogen ions are reduced to produce hydrogen gas and hydroxide ions.

The overall reaction can be summarized as:
$$
\text{Mg} + 2H_2O \rightarrow \text{Mg(OH)}_2 + H_2\uparrow
$$
This process leads to the formation of a magnesium hydroxide ($$Mg(OH)_2$$) layer on the surface of the alloy, which plays a role in corrosion protection but is often porous and not fully protective[1][2].

### 9.1.2 **2. Corrosion Products and Film Formation**
The initial corrosion product is magnesium hydroxide ($$Mg(OH)_2$$), which forms a surface film. However, this film is often porous and allows further penetration of corrosive agents like chloride ions (Cl $$^-$$), especially in environments containing salts (e.g., seawater). Chloride ions can react with the $$Mg(OH)_2$$ film to form soluble magnesium chloride ($$MgCl_2$$), which accelerates the degradation process:
$$
\text{Mg(OH)}_2 + 2Cl^- \rightarrow \text{MgCl}_2 + 2OH^-
$$
This reaction breaks down the protective layer, exposing fresh Mg to further corrosion[1][3].

### 9.1.3 **3. Localized Corrosion Mechanisms**
Several localized corrosion mechanisms are prominent in Mg alloys:

- **Pitting Corrosion**: This occurs when small areas of the surface become anodic relative to the surrounding areas, leading to localized dissolution. Pitting is often exacerbated by impurities or second-phase particles in the alloy that create micro-galvanic cells[1][4].
  
- **Galvanic Corrosion**: When Mg alloys are in contact with more noble metals (such as iron or copper), galvanic corrosion can occur. The Mg acts as the anode and corrodes preferentially due to its lower electrode potential[1][3].

- **Filiform Corrosion**: This occurs under coatings or films on Mg alloys, where an active anode moves along grain boundaries, leading to filament-like corrosion patterns[3].

### 9.1.4 **4. Role of Alloying Elements**
The corrosion resistance of Mg alloys can be influenced by alloying elements such as aluminum (Al), zinc (Zn), calcium (Ca), and rare earth elements (RE). For example:
- **Aluminum** improves corrosion resistance by forming a protective $$MgAl_2O_4$$ spinel layer.
- **Calcium** can reduce cathodic activity and stabilize impurities, improving the overall protective properties of the surface film[5][6].
- **Rare earth elements** enhance the formation of a more stable oxide layer that impedes further corrosion[5].

### 9.1.5 **5. Hydrogen Evolution and Embrittlement**
During corrosion, hydrogen gas is produced at cathodic sites. This hydrogen can sometimes be absorbed into the metal matrix, potentially leading to hydrogen embrittlement—a phenomenon that weakens the material by causing cracks[1].

### 9.1.6 **6. Environmental Factors**
The corrosiveness of Mg alloys is also influenced by environmental factors such as pH, temperature, and the presence of aggressive ions like chlorides. Acidic conditions tend to accelerate corrosion by dissolving protective films more rapidly, while neutral or alkaline conditions may promote passivation through thicker $$Mg(OH)_2$$ layers[7].

In summary, the corrosion mechanism of Mg alloys involves electrochemical reactions that lead to magnesium dissolution and hydrogen evolution. The formation of porous protective films like $$Mg(OH)_2$$, localized attack due to galvanic effects, and environmental factors all contribute to the high susceptibility of these alloys to degradation.

Citations:
[1] https://pmc.ncbi.nlm.nih.gov/articles/PMC9000648/
[2] https://pmc.ncbi.nlm.nih.gov/articles/PMC9504397/
[3] https://www.mdpi.com/2079-6412/13/9/1533
[4] https://pubs.rsc.org/en/content/articlelanding/2023/nj/d3nj02172f
[5] https://www.nature.com/articles/s41467-021-24939-3
[6] https://www.chemistryworld.com/news/new-magnesium-alloy-shows-exceptional-corrosion-resistance/4013323.article
[7] https://www.mdpi.com/2073-4352/13/12/1684
[8] https://tft-pneumatic.com/blog/how-magnesium-alloys-corrode/
[9] https://www.mdpi.com/2624-5558/3/4/31

# 10 Mg合金腐蚀解决: 计算角度
[[computing for Mg alloy design]]







# 11 PPT questions


### 11.1.1 **Project 1: Reactive Force Fields for Zr-Alloy Systems**

==4. What challenges do experimental studies face in understanding atomic-scale damage and corrosion in zirconium alloys?==


19. Difficulty observing real-time atomic processes during corrosion due to the extreme reactor conditions (high temperature, pressure, radiation)

20. Limited ability to isolate and study individual mechanisms since multiple processes occur simultaneously (oxidation, hydride formation, radiation damage)

21. Challenges in characterizing very thin oxide layers and subsurface regions without disrupting them

22. Complex interactions between alloying elements, defects, and corrosion that are hard to measure directly

These limitations make it challenging to fully understand the fundamental mechanisms at play.


==5. How does your ReaxFF-based model improve the simulation of zirconium-alloy-water systems compared to existing methods?==
The new Zr-Nb-H-O ReaxFF model improves simulations in several key ways:

23. More accurate prediction of defect behavior (vacancies and interstitials) compared to previous models

24. Better representation of surface chemistry, particularly H and O atom adsorption energies

25. First model to properly include Nb alloying effects and their impact on corrosion

26. More realistic energetics for H and O dissolution in the bulk material

27. Validated results that match experimental observations, like increased suboxide layer thickness with Nb addition

These improvements allow for more reliable atomic-scale simulations of corrosion mechanisms under reactor conditions.




==1. You mentioned studying self-interstitials in Zr. Could you explain the significance of these defects in material stability?==
Self-interstitials are critical defects in zirconium that significantly impact its stability and mechanical properties. When self-interstitial atoms are present, they introduce localized lattice distortions due to the increased atomic volume in certain regions. This can lead to residual stress and strain, causing material deformation.

Over time, these defects can contribute to phenomena like radiation-induced growth, where the material undergoes anisotropic dimensional changes without external stress. Such changes degrade the mechanical properties of zirconium, such as its strength and ductility, which are crucial for its performance in demanding environments like nuclear reactors. Understanding these effects is essential for predicting and improving the material's stability and reliability

---
### 11.1.2 **Project 4: Machine Learning Models for Stability Predictions**

==14. How does SHAP improve feature importance analysis in machine learning, and why was it essential for your study?==
Consistent Interpretation
SHAP values offer a mathematically rigorous way to measure feature contributions based on game theory principles . The values remain consistent even when model architecture or parameters change, ensuring reliable interpretations across different scenarios 3.
Dual Interpretability Levels
Global Understanding: SHAP reveals overall feature importance patterns and whether features have positive or negative impacts on predictions across the entire dataset 2.
Local Explanations: It provides detailed insights into how features influence individual predictions, unlike traditional methods that only show aggregated results 2.


2

